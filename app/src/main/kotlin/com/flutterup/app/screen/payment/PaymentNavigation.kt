package com.flutterup.app.screen.payment

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.toRoute
import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.PaymentExchangeType
import kotlinx.serialization.Serializable

@Serializable data class PaymentSubscriptionRoute(
    val eventFrom: AppPaymentFrom, //页面来源
    val expireTime: Long? = null, //过期时间
    val cancelable: Boolean = true, //是否可以取消
)

@Serializable data class PaymentPacksRoute(
    val type: PaymentExchangeType = PaymentExchangeType.ALL, //排序方式
    val requiredNum: Int? = null,
)

@Serializable data class PaymentDiamondsRoute(
    val from: AppPaymentFrom, //页面来源
    val requiredDiamonds: Float? = null,
)

fun NavGraphBuilder.paymentGraph() {
    composable<PaymentSubscriptionRoute> {
        val data = it.toRoute<PaymentSubscriptionRoute>()

        PaymentSubscriptionScreen(
            eventFrom = data.eventFrom,
            expireTime = data.expireTime,
            cancelable = data.cancelable,
        )
    }

    dialog<PaymentPacksRoute> {
        val data = it.toRoute<PaymentPacksRoute>()
        PaymentPacksScreen(exchangeType = data.type, requiredNum = data.requiredNum)
    }

    dialog<PaymentDiamondsRoute> {
        val data = it.toRoute<PaymentDiamondsRoute>()
        PaymentDiamondsScreen(
            from = data.from,
            requiredDiamonds = data.requiredDiamonds,
        )
    }
}