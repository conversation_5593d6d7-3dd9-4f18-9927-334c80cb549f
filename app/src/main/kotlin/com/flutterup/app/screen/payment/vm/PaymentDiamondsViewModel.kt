package com.flutterup.app.screen.payment.vm

import com.flutterup.app.billing.AppBillingMonitor
import com.flutterup.app.billing.AppBillingProduct
import com.flutterup.app.model.StoreProductEntity
import com.flutterup.app.screen.payment.state.PaymentDiamondsUiState
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseViewModel
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class PaymentDiamondsViewModel @Inject constructor(
    private val billingMonitor: AppBillingMonitor,
    private val userMonitor: UserMonitor,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(PaymentDiamondsUiState())
    private val _productStateFlow: Flow<StoreProductEntity?> = billingMonitor.product.productStateFlow.map {
        when (it) {
            is AppBillingProduct.ProductState.Success -> it.inapp
            else -> null
        }
    }

    val uiState = combine(
        _uiState,
        _productStateFlow,
        userMonitor.userInfoState,
        loadingState,
    ) { ui, product, userInfo, loading ->
        ui.copy(
            diamonds = userInfo?.right?.diamonds ?: 0f,

            diamondsProducts = product?.products.orEmpty(),
            description1 = product?.pageDesc1,
            description2 = product?.pageDesc2,

            isLoading = loading
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun updateRequiredDiamonds(requiredDiamonds: Float?) {
        _uiState.update { it.copy(requiredDiamonds = requiredDiamonds) }
    }

    fun updateCurrentDiamondsPackId(id: String?) {
        _uiState.update { it.copy(currentDiamondsProductId = id) }
    }

    fun pay() {
        Timber.showToast("点击了购买, 商品ID：${uiState.value.currentDiamondsProductId}")
    }
}