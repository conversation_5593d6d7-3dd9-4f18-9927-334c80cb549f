package com.flutterup.app.screen.chat.vm

import com.flutterup.app.model.ExchangePacksRequest
import com.flutterup.app.model.MediaMessageEntity
import com.flutterup.app.model.MultiMediaMessageEntity
import com.flutterup.app.model.UnwrapGiftRequest
import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import com.flutterup.chat.message.content.GiftMessageContent
import com.flutterup.chat.message.content.MultiPrivateMessageContent
import com.flutterup.chat.message.content.PrivateMessageContent
import com.flutterup.gifts.entity.GiftEntity
import com.flutterup.gifts.entity.GiftResourceInfo
import io.rong.imlib.model.Message
import javax.inject.Inject

class ChatApiServiceRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {


    suspend fun triggerCustomerServiceGreeting(targetId: String): Boolean {
        try {
            val result = apiService.triggerCustomerServiceGreeting(targetId)
            return result.isSuccess
        } catch (_: Exception) {
            return false
        }
    }


    suspend fun exchangeGift(giftEntity: GiftEntity): String? {
        val request = ExchangePacksRequest(
            id = giftEntity.giftId,
            num = 1,
            price = giftEntity.price ?: 0
        )
        val result = apiService.exchangePacks(request)
        return result.data?.id
    }

    suspend fun openPrivateMessage(message: Message?): Boolean {
        if (message == null) return false

        val toMsg = message.uId
        val fromMsg: String?
        val id: Long?

        when(val content = message.content) {
            is PrivateMessageContent -> {
                val entity = content.get(MediaMessageEntity::class.java)
                fromMsg = entity?.messageId
                id = entity?.id
            }
            is MultiPrivateMessageContent -> {
                val entity = content.get(MultiMediaMessageEntity::class.java)
                fromMsg = entity?.messageId
                id = null
            }
            else -> {
                fromMsg = null
                id = null
            }
        }

        if (fromMsg == null) return false

        val result = apiService.openPrivateMessage(fromMsg, toMsg, id)
        return result.isSuccess
    }

    /**
     * 拆封礼物
     */
    suspend fun unwrapGift(message: Message?): Boolean {
        if (message == null) return false

        val toMsg = message.uId
        val fromMsg = (message.content as? GiftMessageContent)?.get(GiftResourceInfo::class.java)?.originalMsgId ?: return false

        val result = apiService.unwrapGift(UnwrapGiftRequest(fromMsg, toMsg))
        return result.isSuccess
    }
}