package com.flutterup.app.screen.payment.vm

import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.ExchangeItem
import com.flutterup.app.model.ExchangeListRequest
import com.flutterup.app.model.ExchangeListResponse
import com.flutterup.app.model.ExchangePacksRequest
import com.flutterup.app.model.PaymentExchangeType
import com.flutterup.app.model.ProductItem
import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class PaymentPacksRepository @Inject constructor(
    private val apiService: ApiService,
) : BaseRepository() {

    suspend fun getExchangeList(exchangeType: PaymentExchangeType, requiredNum: Int?): ExchangeListResponse? {
        val result = apiService.getExchangeList(request = ExchangeListRequest(
            type = exchangeType.alias,
            requiredNum = requiredNum,
        ))

        return result.data
    }

    suspend fun exchange(
        exchangeItem: ExchangeItem,
        num: Int,
        payFrom: AppPaymentFrom = AppPaymentFrom.UNKNOWN
    ): Boolean {
        val request = ExchangePacksRequest(
            id = exchangeItem.id.orEmpty(),
            num = num,
            price = exchangeItem.price ?: 0,
            eventFrom = payFrom.value
        )
        return apiService.exchangePacks(request = request).isSuccess
    }
}