package com.flutterup.app.screen.relate.vm

import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.WinkItemEntity
import com.flutterup.app.model.WinkReadType
import com.flutterup.app.model.WinkType
import com.flutterup.app.screen.profile.vm.UserActionRepository
import com.flutterup.app.screen.relate.state.WinksReceivedState
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class WinksReceivedViewModel @Inject constructor(
    private val repository: WinksRepository,
    private val userMonitor: UserMonitor,
    private val userActionRepository: UserActionRepository,
) : BaseRepositoryViewModel(repository, userActionRepository) {

    private val _uiState = MutableStateFlow(WinksReceivedState())

    val uiState: StateFlow<WinksReceivedState> = combine(
        _uiState,
        userMonitor.userInfoState
    ) { ui, userInfo ->
        ui.copy(
            isVip = userInfo?.right?.vip == 1,
            isStealthMode = userInfo?.isHide == 1
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )

    init {
        refresh()
    }

    /**
     * 刷新
     */
    fun refresh() {
        scope.launch {
            _uiState.update { it.copy(isRefreshing = true) }

            val result = repository.getWinksList(WinkType.Received)
            result?.let { result ->
                _uiState.update { it.copy(winks = result.list.orEmpty()) }
            }
        }.invokeOnCompletion {
            _uiState.update { it.copy(isRefreshing = false) }
            markAllAsRead(isUpdateUi = false)
        }
    }

    fun loadMore() {
        scope.launch {
            _uiState.update { it.copy(isLoadingMore = true) }
            val lastId = _uiState.value.winks.lastOrNull()?.id
            val result = repository.getWinksList(WinkType.Received, lastId)
            result?.let { result ->
                _uiState.update { it.copy(winks = it.winks + result.list.orEmpty()) }
            }
        }.invokeOnCompletion {
            _uiState.update { it.copy(isLoadingMore = false) }
        }
    }

    fun markAllAsRead(isUpdateUi: Boolean = true) {
        scope.launch {
            val readWinksResult = repository.markAllWinksRead(WinkReadType.Received)
            if (readWinksResult && isUpdateUi) {
                _uiState.update { it.copy(winks = it.winks.map { entity -> entity.copy(read = 1) }) }
            }
        }
    }

    fun markAsReadThenRefresh() {
        scope.launch {
            _uiState.update { it.copy(isRefreshing = true) }
            val readWinksResult = repository.markAllWinksRead(WinkReadType.Received)
            if (readWinksResult) {
                refresh()
            }
        }
    }

    fun dislike(winkItem: WinkItemEntity) {
        scope.launch {
            val isSuccess = userActionRepository.dislike(winkItem.userId.orEmpty(), AppFrom.WinkReceived)
            if (isSuccess) {
                _uiState.update { it.copy(winks = it.winks.filter { entity -> entity.id != winkItem.id }) }
            }
        }
    }

    fun like(winkItem: WinkItemEntity) {
        scope.launch {
            val isSuccess = userActionRepository.like(winkItem.userId.orEmpty(), AppFrom.WinkReceived)
            if (isSuccess) {
                _uiState.update { it.copy(winks = it.winks.filter { entity -> entity.id != winkItem.id }) }
            }
        }
    }
}