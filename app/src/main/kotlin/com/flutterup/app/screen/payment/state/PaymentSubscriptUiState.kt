package com.flutterup.app.screen.payment.state

import com.flutterup.app.model.ProductBenefitEntity
import com.flutterup.app.model.ProductItem
import com.flutterup.base.BaseState

data class PaymentSubscriptUiState(

    val benefits: List<ProductBenefitEntity> = emptyList(),

    val items: List<ProductItem> = emptyList(),

    val selected: Int = with(items.indexOfFirst { it.hot == 1 }) { if (this == -1) 0 else this },

    val description1: String? = null,

    val description2: String? = null,

    val isVip: Boolean = false,

    override val isLoading: Boolean = false,
) : BaseState

