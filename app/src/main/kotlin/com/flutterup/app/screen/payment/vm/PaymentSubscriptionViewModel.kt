package com.flutterup.app.screen.payment.vm

import com.flutterup.app.billing.AppBillingMonitor
import com.flutterup.app.billing.AppBillingProduct
import com.flutterup.app.screen.payment.state.PaymentSubscriptUiState
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class PaymentSubscriptionViewModel @Inject constructor(
    private val userMonitor: UserMonitor,
    private val billingMonitor: AppBillingMonitor,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(PaymentSubscriptUiState())
    private val _productStateFlow = billingMonitor.product.productStateFlow.map {
        when (it) {
            is AppBillingProduct.ProductState.Success -> it.subscription
            else -> null
        }
    }

    val uiState = combine(
        _uiState,
        _productStateFlow,
        userMonitor.userInfoState,
        loadingState
    ) { ui, product, userInfo, loading ->
        ui.copy(
            isVip = userInfo?.right?.vip == 1,

            benefits = product?.benefits ?: emptyList(),
            items = product?.products ?: emptyList(),
            description1 = product?.pageDesc1,
            description2 = product?.pageDesc2,

            isLoading = loading
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun updateSelected(selectedIndex: Int) {
        _uiState.update { it.copy(selected = selectedIndex) }
    }

    fun pay() {

    }

    fun logout() {
        userMonitor.logout()
    }
}
