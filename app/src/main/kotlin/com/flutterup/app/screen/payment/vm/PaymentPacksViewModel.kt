package com.flutterup.app.screen.payment.vm

import android.content.Context
import com.flutterup.app.R
import com.flutterup.app.model.PaymentExchangeType
import com.flutterup.app.screen.common.vm.ProfileSharedRepository
import com.flutterup.app.screen.payment.state.PacksQuantity
import com.flutterup.app.screen.payment.state.PaymentPacksUiState
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseViewModel
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PaymentPacksViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userMonitor: UserMonitor,
    private val repository: PaymentPacksRepository,
    private val profileSharedRepository: ProfileSharedRepository,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(PaymentPacksUiState())

    val uiState = combine(
        _uiState,
        userMonitor.userInfoState,
        loadingState,
    ) { ui, userinfo, loading ->
        ui.copy(
            diamonds = userinfo?.right?.diamonds ?: 0F,
            isLoading = loading
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun init(exchangeType: PaymentExchangeType, requiredNum: Int?) {
        _uiState.update { it.copy(exchangeType = exchangeType) }

        scope.launch {
            val data = repository.getExchangeList(exchangeType, requiredNum)

            _uiState.update { it.copy(exchangeList = data?.list.orEmpty(), description = data?.desc) }
        }
    }

    fun updateCurrentExchangeQuantity(newExchangeQuantity: Int?) {
        if (newExchangeQuantity == null) {
            Timber.showToast(quantityRangeText)
            _uiState.update { it.copy(currentExchangeQuantity = PacksQuantity.MIN) }
            return
        }

        if (newExchangeQuantity !in PacksQuantity.RANGE) {
            Timber.showToast(quantityRangeText)
        }

        _uiState.update { it.copy(currentExchangeQuantity = newExchangeQuantity.coerceIn(PacksQuantity.RANGE)) }
    }

    fun updateCurrentProductId(id: String?) {
        _uiState.update { it.copy(currentId = id) }
    }

    fun exchange(onSuccess: () -> Unit = {}) {
        val currentProd = uiState.value.currentProduct ?: return
        val quantity = uiState.value.currentExchangeQuantity

        scope.launchWithLoading {
            val isSuccess = repository.exchange(currentProd, quantity)
            if (isSuccess) {
                profileSharedRepository.updateMineRights()
                onSuccess()
            }
        }
    }

    private val quantityRangeText = context.getString(
        R.string.packs_exchange_limit, PacksQuantity.MIN, PacksQuantity.MAX
    )
}
