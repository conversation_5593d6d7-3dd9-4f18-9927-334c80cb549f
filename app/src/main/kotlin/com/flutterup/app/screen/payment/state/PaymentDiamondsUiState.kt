package com.flutterup.app.screen.payment.state

import com.flutterup.app.model.ProductItem
import com.flutterup.base.BaseState
import org.jetbrains.annotations.TestOnly

data class PaymentDiamondsUiState(
    val diamonds: Float = 0.0f,

    val requiredDiamonds: Float? = null,

    val currentDiamondsProductId: String? = null,

    val diamondsProducts: List<ProductItem> = emptyList(),

    val description1: String? = null,

    val description2: String? = null,

    override val isLoading: Boolean = false,
) : BaseState {

    val selectedProductId: String? = currentDiamondsProductId ?: calculateSelectedProductId()

    /**
     * 计算选中的商品ID
     *
     */
    private fun calculateSelectedProductId(): String? {
        if (requiredDiamonds == null) {
            return diamondsProducts.firstOrNull { it.hot == 1 }?.prodId
        }

        return diamondsProducts.firstOrNull {
            (it.time?.toFloatOrNull() ?: 0F) <= requiredDiamonds
        }?.prodId
    }

    companion object {
        @TestOnly
        val TEST = PaymentDiamondsUiState(
            diamonds = 100f,
            diamondsProducts = listOf(
                ProductItem(
                    prodId = "1",
                    hot = 1,
                    time = "100",
                    price = "100",
                    name = "100",
                    icon = "100",
                )
            )
        )
    }
}