@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.relate

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBackground
import com.flutterup.app.design.component.AppLoadMoreIndicator
import com.flutterup.app.design.component.AppPlaceholder
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.WinkItemEntity
import com.flutterup.app.model.WinkType
import com.flutterup.app.screen.LocalAppState
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.SendPingChatRoute
import com.flutterup.app.screen.profile.ProfileOtherRoute
import com.flutterup.app.screen.relate.component.WinksItem
import com.flutterup.app.screen.relate.state.WinksReceivedState
import com.flutterup.app.screen.relate.vm.WinksReceivedViewModel
import com.flutterup.base.compose.refresh.RefreshLoadMoreLazyColumn
import com.flutterup.base.compose.refresh.rememberPullToLoadMoreState


@Composable
fun WinksReceivedScreen() {
    val navController = LocalNavController.current
    val navCenter = LocalAppState.current.navCenter
    
    val viewModel: WinksReceivedViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    //再次进入，有未读消息时，标记为已读
    LaunchedEffect(uiState.hasUnreadWink) {
        if (uiState.hasUnreadWink) {
            viewModel.markAllAsRead()
        }
    }

    WinksReceivedContent(
        uiState = uiState,
        onRefresh = { viewModel.markAsReadThenRefresh() },
        onLoadMore = { viewModel.loadMore() },
        onAvatarClick = {
            if (!uiState.isVip) {
                navCenter.navigateToVipDialog(AppPaymentFrom.WLM_LIKE_LIMIT)
                return@WinksReceivedContent
            }
            navController.navigate(
                ProfileOtherRoute(
                    userId = it.userId.orEmpty(),
                    from = AppFrom.WinkReceived
                )
            )
        },
        onDislikeClick = { viewModel.dislike(it) },
        onLikeClick = { viewModel.like(it) },
        onBottomClick = {
            val userId = it.userId ?: return@WinksReceivedContent
            navController.navigate(SendPingChatRoute(userId, AppFrom.WinkReceived))
        }
    )
}

@Composable
private fun WinksReceivedContent(
    uiState: WinksReceivedState,
    onRefresh: () -> Unit = {},
    onLoadMore: () -> Unit = {},
    onAvatarClick: (WinkItemEntity) -> Unit = {},
    onDislikeClick: (WinkItemEntity) -> Unit = {},
    onLikeClick: (WinkItemEntity) -> Unit = {},
    onBottomClick: (WinkItemEntity) -> Unit = {},
) {
    val pullToLoadMoreState = rememberPullToLoadMoreState()

    if (uiState.isStealthMode && uiState.isVip) {
        AppPlaceholder(Modifier.fillMaxSize()) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Image(
                    painter = painterResource(id = R.mipmap.ic_winks_stealth_mode_on),
                    contentDescription = null
                )

                Text(
                    text = stringResource(R.string.winks_received_stealth_mode_title),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 22.sp,
                        fontWeight = FontWeight.W400,
                        color = TextBlack,
                    ),
                    modifier = Modifier.offset(y = (-15).dp)
                )

                Text(
                    text = stringResource(R.string.winks_received_stealth_mode_content),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W400,
                        color = TextBlack666.copy(alpha = 0.62f),
                    ),
                    modifier = Modifier.offset(y = (-15).dp)
                )
            }
        }
        return
    }

    RefreshLoadMoreLazyColumn(
        isRefreshing = uiState.isRefreshing,
        isLoadingMore = uiState.isLoadingMore,
        hasNoMoreData = uiState.hasNoMoreData,
        onRefresh = onRefresh,
        onLoadMore = onLoadMore,
        pullLoadMoreState = pullToLoadMoreState,
        verticalArrangement = Arrangement.spacedBy(10.dp),
        contentPadding = PaddingValues(horizontal = 12.dp),
        loadMoreIndicator = {
            AppLoadMoreIndicator(
                isLoadingMore = uiState.isLoadingMore,
                hasNoMoreData = uiState.hasNoMoreData,
                state = pullToLoadMoreState
            )
        },
        placeholderEnable = uiState.winks.isEmpty(),
        placeholder = {
            AppPlaceholder(Modifier.fillMaxSize())
        }
    ) {
        items(uiState.winks, key = { it.id }) {
            WinksItem(
                winkType = WinkType.Received,
                item = it,
                blurEnabled = !uiState.isVip,
                onAvatarClick = { onAvatarClick(it) },
                onDislikeClick = { onDislikeClick(it) },
                onLikeClick = { onLikeClick(it) },
                onBottomClick = { onBottomClick(it) }
            )
        }
    }
}

@Preview
@Composable
private fun WinksReceivedScreenPreview() {
    AppTheme {
        AppBackground {
            WinksReceivedContent(
                uiState = WinksReceivedState(isVip = true, isStealthMode = true)
            )
        }
    }
}