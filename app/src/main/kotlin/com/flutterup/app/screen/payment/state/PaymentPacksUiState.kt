package com.flutterup.app.screen.payment.state

import androidx.compose.runtime.Immutable
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import com.flutterup.app.R
import com.flutterup.app.design.icons.None
import com.flutterup.app.design.icons.PacksPingChat
import com.flutterup.app.design.icons.PacksPrivatePhoto
import com.flutterup.app.design.icons.PacksPrivateVideo
import com.flutterup.app.model.ExchangeItem
import com.flutterup.app.model.PaymentExchangeType
import com.flutterup.app.model.PaymentExchangeType.ALL
import com.flutterup.app.model.PaymentExchangeType.PHOTO
import com.flutterup.app.model.PaymentExchangeType.VIDEO
import com.flutterup.app.model.PaymentExchangeType.PING_CHAT
import com.flutterup.base.BaseState
import com.flutterup.base.utils.toDecimalSubstring
import org.jetbrains.annotations.TestOnly

data class PaymentPacksUiState(
    /** 当前钻石数量 */
    val diamonds: Float = 0f,

    /** 当前交换数量, 默认为1, 也可能不为1 */
    val currentExchangeQuantity: Int = 1,

    /** 当前选中item */
    val currentId: String? = null,

    /** 商品列表 */
    val exchangeList: List<ExchangeItem> = emptyList(),

    /** 筛选方式 */
    val exchangeType: PaymentExchangeType = ALL,

    val description: String? = null,

    /** 是否加载中 */
    override val isLoading: Boolean = false,
) : BaseState {
    /**
     * 根据商品类型分类
     */
    val classificationByType: Map<PaymentPacksType, List<ExchangeItem>> = exchangeList.groupBy { PaymentPacksType.fromValue(it.type) }

    /**
     * 当前选中的商品
     */
    val currentProduct: ExchangeItem? = exchangeList.firstOrNull { it.id == currentId } ?: calculateCurrentProducts()

    /**
     * 是否能够减1, 选中商品并且数量大于1
     */
    val negativeEnable: Boolean = currentExchangeQuantity > PacksQuantity.MIN && currentProduct != null

    /**
     * 是否能够加1, 选中商品并且最多只能选99个
     */
    val positiveEnable: Boolean = currentProduct != null && currentExchangeQuantity < PacksQuantity.MAX

    /**
     * 需要多少钻石
     */
    val neededDiamonds: String? = calculateNeededDiamonds()

    /**
     * 根据排序方式排序
     */
    val orderedProducts: List<Pair<PaymentPacksType, List<ExchangeItem>>> = initializeOrderedProducts()

    /**
     * 计算总共需要多少钻石
     */
    private fun calculateNeededDiamonds(): String? {
        val everyDiamond = currentProduct?.price ?: return null
        if (everyDiamond <= 0) {
            return null
        }

        return (everyDiamond * currentExchangeQuantity).toDecimalSubstring()
    }

    private fun calculateCurrentProducts(): ExchangeItem? {
        return when(exchangeType) {
            ALL -> classificationByType[PaymentPacksType.PrivatePhoto]?.firstOrNull { it.hot }
            PHOTO -> classificationByType[PaymentPacksType.PrivatePhoto]?.firstOrNull { it.hot }
            VIDEO -> classificationByType[PaymentPacksType.PrivateVideo]?.firstOrNull { it.hot }
            PING_CHAT -> classificationByType[PaymentPacksType.PingChat]?.firstOrNull { it.hot }
        }
    }

    private fun initializeOrderedProducts() = when (exchangeType) {
        ALL -> listOf(
            Pair(PaymentPacksType.PrivatePhoto, classificationByType[PaymentPacksType.PrivatePhoto] ?: emptyList()),
            Pair(PaymentPacksType.PrivateVideo, classificationByType[PaymentPacksType.PrivateVideo] ?: emptyList()),
            Pair(PaymentPacksType.PingChat, classificationByType[PaymentPacksType.PingChat] ?: emptyList()),
        )
        PHOTO -> listOf(
            Pair(PaymentPacksType.PrivatePhoto, classificationByType[PaymentPacksType.PrivatePhoto] ?: emptyList())
        )
        VIDEO -> listOf(
            Pair(PaymentPacksType.PrivateVideo, classificationByType[PaymentPacksType.PrivateVideo] ?: emptyList()),
        )
        PING_CHAT -> listOf(
            Pair(PaymentPacksType.PingChat, classificationByType[PaymentPacksType.PingChat] ?: emptyList()),
        )
    }

    companion object {
        /**
         * todo 替换成非测试代码
         */
        @TestOnly
        val TEST = PaymentPacksUiState(
            diamonds = 105f,
            currentExchangeQuantity = 1,
            exchangeList = listOf(
                ExchangeItem(
                    id = "1",
                    price = 100,
                    name = "100",
                    num = 10,
                    type = "pp"
                ),
                ExchangeItem(
                    id = "2",
                    price = 200,
                    name = "200",
                    num = 20,
                    type = "pp",
                    hot = true
                ),
                ExchangeItem(
                    id = "3",
                    price = 300,
                    name = "300",
                    num = 30,
                    type = "pp"
                ),
                ExchangeItem(
                    id = "4",
                    price = 400,
                    name = "400",
                    num = 40,
                    type = "pv"
                ),
                ExchangeItem(
                    id = "5",
                    price = 500,
                    name = "500",
                    num = 50,
                    type = "pv",
                    hot = true
                ),
                ExchangeItem(
                    id = "6",
                    price = 600,
                    name = "600",
                    num = 60,
                    type = "pv"
                ),
                ExchangeItem(
                    id = "7",
                    price = 700,
                    name = "700",
                    num = 70,
                    type = "fc"
                ),
                ExchangeItem(
                    id = "8",
                    price = 800,
                    name = "800",
                    num = 80,
                    type = "fc",
                    hot = true
                ),
                ExchangeItem(
                    id = "9",
                    price = 900,
                    name = "900",
                    num = 90,
                    type = "fc"
                ),
            ),
            description = "Feel free to cancel your subscription whenever you like.Your subscription fee will be charged to your Google Play account and will auto-renew 24 hours before the current period ends. You can disable auto-renewal anytime in your Google Play account settings. No refunds are available once the subscription is active, so please cancel before the current period ends to avoid charges. By clicking \"Continue,\" you agree to our Privacy Policy and Terms of Service."
        )
    }
}

enum class PaymentPacksType(
    val alias: String?,
    val icon: ImageVector,
    val titleResources: Int,
    val colors: PacksColors,
) {
    Unknown(null, ImageVector.None, 0, PacksColorDefaults.unknown),
    PrivatePhoto("pp", PacksPrivatePhoto, R.string.private_photo, PacksColorDefaults.privatePhoto),
    PrivateVideo("pv", PacksPrivateVideo, R.string.private_video, PacksColorDefaults.privateVideo),
    PingChat("fc", PacksPingChat, R.string.ping_chat, PacksColorDefaults.pingChat);


    companion object {
        fun fromValue(alias: String?): PaymentPacksType {
            return entries.firstOrNull { it.alias == alias } ?: Unknown
        }
    }
}

object PacksQuantity {
    const val MIN = 1

    const val MAX = 99

    val RANGE = IntRange(MIN, MAX)
}

@Immutable
class PacksColors
internal constructor(
    val topCardBackground: Color,
    val packsBackground: Brush,
    val textColor: Color,
    val labelColor: Color,
    val outlineColor: Color = textColor,
)

private object PacksColorDefaults {
    val unknown = PacksColors(
        topCardBackground = Color.Unspecified,
        packsBackground = Brush.verticalGradient(listOf(Color.Unspecified)),
        textColor = Color.Unspecified,
        labelColor = Color.Unspecified,
    )

    val privatePhoto = PacksColors(
        topCardBackground = Color(0xFFFFF1E4),
        packsBackground = Brush.verticalGradient(
            colors = listOf(Color(0xFFFFDEAB), Color(0xFFFFE3BB))
        ),
        textColor = Color(0xFFD27B57),
        labelColor = Color(0xFFFFDEAB),
    )

    val privateVideo = PacksColors(
        topCardBackground = Color(0xFFEEE4FF),
        packsBackground = Brush.verticalGradient(
            colors = listOf(Color(0xFFC2ABFF), Color(0xFFE4BBFF))
        ),
        textColor = Color(0xFF680AB2),
        labelColor = Color(0xFFC2ABFF),
    )

    val pingChat = PacksColors(
        topCardBackground = Color(0xFFE4E9FF),
        packsBackground = Brush.verticalGradient(
            colors = listOf(Color(0xFFA9B9FF), Color(0xFFD5DFFF))
        ),
        textColor = Color(0xFF4040FF),
        labelColor = Color(0xFFA9B9FF),
    )
}