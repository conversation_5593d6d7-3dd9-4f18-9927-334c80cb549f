package com.flutterup.app.screen.common.vm

import android.annotation.SuppressLint
import com.flutterup.base.BaseViewModel
import com.flutterup.base.utils.LocationUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.TimeZone
import javax.inject.Inject

@HiltViewModel
class PermissionSharedViewModel @Inject constructor(
    private val permissionRepository: PermissionRepository,
) : BaseViewModel() {

    @SuppressLint("MissingPermission")
    fun upgradeLocationPermission(enable: Boolean) {
        scope.launch {
            val location = if (enable) LocationUtils.getCurrentLocation() else null
            permissionRepository.upgradePermissionStatus(
                locationStatus = if (enable) PermissionRepository.PERMISSION_ON else PermissionRepository.PERMISSION_OFF,
                states = location?.state,
                latitude = location?.lat,
                longitude = location?.lng,
                country = location?.country,
                timeZero = TimeZone.getDefault().id
            )
        }
    }

    fun upgradeNotificationPermission(enable: Boolean) {
        scope.launch {
            permissionRepository.upgradePermissionStatus(noticeStatus = if (enable) PermissionRepository.PERMISSION_ON else PermissionRepository.PERMISSION_OFF)
        }
    }
}