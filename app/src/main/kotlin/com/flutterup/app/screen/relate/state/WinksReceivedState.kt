package com.flutterup.app.screen.relate.state

import com.flutterup.app.model.WinkItemEntity

data class WinksReceivedState(
    val isRefreshing: Boolean = false,
    val isLoadingMore: Boolean = false,
    val hasNoMoreData: Boolean = false,

    val isVip: Boolean = false,
    val isStealthMode: Boolean = false,

    val winks: List<WinkItemEntity> = emptyList(),
) {

    val hasUnreadWink = winks.isNotEmpty() && winks.any { it.read == 0 }
}