package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@Keep
@JsonClass(generateAdapter = true)
data class ExchangeListRequest(
    @Json(name = "type")
    val type: String? = null,

    @Json(name = "need_num")
    val requiredNum: Int? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class ExchangeListResponse(
    @Json(name = "list")
    val list: List<ExchangeItem>? = null,

    @Json(name = "description")
    val desc: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class ExchangeItem(
    @Json(name = "type")
    val type: String? = null,

    @Json(name = "id")
    val id: String? = null,

    @Json(name = "hot")
    val hot: Boolean = false,

    @<PERSON>son(name = "num")
    val num: Int? = null,

    @Json(name = "name")
    val name: String? = null,

    @<PERSON><PERSON>(name = "desc")
    val desc: String? = null,

    @Json(name = "price")
    val price: Int? = null,
)





/**
 * @param id 权益/礼物id
 * @param num 数量，gift默认永远为1
 * @param price 价格
 */
@Keep
@JsonClass(generateAdapter = true)
data class ExchangePacksRequest(
    @Json(name = "product_id")
    val id: String,

    @Json(name = "num")
    val num: Int,

    @Json(name = "price")
    val price: Int,

    @Json(name = "event_from")
    val eventFrom: Int? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class ExchangeGiftResponse(
    @Json(name = "id")
    val id: String? = null
)

