package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass


@Keep
@JsonClass(generateAdapter = true)
data class StoreProductEntity(
    @Json(name = "rights") val benefits: List<ProductBenefitEntity>? = null,
    @Json(name = "shops") val products: List<ProductItem>? = null,
    @Json(name = "page_desc1") val pageDesc1: String? = null,
    @<PERSON><PERSON>(name = "page_desc2") val pageDesc2: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class ProductBenefitEntity(
    @Json(name = "icon") val icon: String? = null,
    @Json(name = "icon_unselect") val iconUnselect: String? = null,
    @Json(name = "title") val title: String? = null,
    @<PERSON><PERSON>(name = "desc") val desc: String? = null,
    @<PERSON>son(name = "background") val background: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class ProductItem(
    @Json(name = "time") val time: String? = null,
    @<PERSON><PERSON>(name = "uint") val unit: String? = null,
    @Json(name = "save") val save: String? = null,
    @Json(name = "price") val price: String? = null,
    @Json(name = "average") val average: String? = null,
    @Json(name = "hot") val hot: Int? = null,
    @Json(name = "prod_id") val prodId: String? = null,
    @Json(name = "product_type") val prodType: String? = null,
    @Json(name = "subscribing") val subscribing: Int? = null,
    @Json(name = "name") val name: String? = null,
    @Json(name = "price_value") val priceValue: Int? = null,
    @Json(name = "price_fvalue") val priceFValue: Float? = null,
    @Json(name = "currency") val currency: String? = null,
    @Json(name = "priceId") val priceId: String? = null,
    @Json(name = "icon") val icon: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class GeneratorOrderEntity(
    @Json(name = "order_id") val orderId: String? = null,
    @Json(name = "prod_id") val prodId: String? = null,
    @Json(name = "product_type") val prodType: String? = null
)

@Keep
@JsonClass(generateAdapter = true)
data class CheckOrderEntity(
    @Json(name = "ok") val ok: Int? = null, // Consider using Boolean (1 = true, 0 = false)
    @Json(name = "order_id") val orderId: String? = null,
    @Json(name = "right") val right: UserRightsEntity? = null, // Assumed from userinfo.api
    @Json(name = "is_first_pay") val isFirstPay: Int? = null, // Consider using Boolean
    @Json(name = "report_money") val reportMoney: Double? = null
)