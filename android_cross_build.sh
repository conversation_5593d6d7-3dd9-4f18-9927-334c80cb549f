#!/bin/bash

# Android Cross-Platform Build Script
# Supports Mac, Windows (WSL/Git Bash), and Linux
# Auto-detects and installs Java and Android SDK if needed

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
JAVA_VERSION="11"
ANDROID_API_LEVEL="33"
BUILD_TOOLS_VERSION="33.0.2"

# Default paths
DEFAULT_PROJECT_PATH="."
DEFAULT_KEYSTORE_PATH=""
DEFAULT_BUILD_TYPE="apk"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to detect operating system
detect_os() {
    case "$OSTYPE" in
        darwin*)  OS="mac" ;;
        linux*)   OS="linux" ;;
        msys*)    OS="windows" ;;
        cygwin*)  OS="windows" ;;
        *)        
            print_error "Unsupported operating system: '$OSTYPE'"
            exit 1
            ;;
    esac
    print_info "Detected OS: $OS"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to download file
download_file() {
    local url=$1
    local output=$2
    
    if command_exists curl; then
        curl -L -o "$output" "$url"
    elif command_exists wget; then
        wget -O "$output" "$url"
    else
        print_error "Neither curl nor wget found. Please install one of them."
        exit 1
    fi
}

# Function to check and install Java
check_install_java() {
    print_info "Checking Java installation..."
    
    if command_exists java && command_exists javac; then
        # Get Java version and extract major version number
        JAVA_VER=$(java -version 2>&1 | head -n 1 | sed 's/.*version "\(.*\)".*/\1/' | cut -d'.' -f1)
        # Handle Java 1.x format (e.g., 1.8 -> 8)
        if [[ "$JAVA_VER" == "1" ]]; then
            JAVA_VER=$(java -version 2>&1 | head -n 1 | sed 's/.*version "1\.\([0-9]*\)".*/\1/')
        fi
        
        # Check if JAVA_VER is a valid number
        if [[ "$JAVA_VER" =~ ^[0-9]+$ ]] && [ "$JAVA_VER" -ge "$JAVA_VERSION" ]; then
            print_success "Java $JAVA_VER is already installed"
            return 0
        elif [[ "$JAVA_VER" =~ ^[0-9]+$ ]]; then
            print_warning "Java version $JAVA_VER is too old. Required: $JAVA_VERSION+"
        else
            print_warning "Could not determine Java version"
        fi
    else
        print_warning "Java not found"
    fi
    
    print_info "Installing Java $JAVA_VERSION..."
    
    case $OS in
        "mac")
            if command_exists brew; then
                brew install openjdk@$JAVA_VERSION
                # Link Java for system-wide use
                sudo ln -sfn /opt/homebrew/opt/openjdk@$JAVA_VERSION/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-$JAVA_VERSION.jdk 2>/dev/null || true
            else
                print_error "Homebrew not found. Please install Homebrew first or install Java manually."
                exit 1
            fi
            ;;
        "linux")
            if command_exists apt-get; then
                sudo apt-get update
                sudo apt-get install -y openjdk-$JAVA_VERSION-jdk
            elif command_exists yum; then
                sudo yum install -y java-$JAVA_VERSION-openjdk-devel
            elif command_exists dnf; then
                sudo dnf install -y java-$JAVA_VERSION-openjdk-devel
            else
                print_error "Package manager not found. Please install Java manually."
                exit 1
            fi
            ;;
        "windows")
            print_error "Automatic Java installation on Windows is not supported."
            print_info "Please download and install Java from: https://adoptopenjdk.net/"
            exit 1
            ;;
    esac
    
    print_success "Java installation completed"
}

# Function to download and install command line tools
download_and_install_cmdline_tools() {
    print_info "Downloading and installing Android SDK command line tools..."

    # Download command line tools
    case $OS in
        "mac")
            SDK_URL="https://dl.google.com/android/repository/commandlinetools-mac-9477386_latest.zip"
            ;;
        "linux")
            SDK_URL="https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip"
            ;;
        "windows")
            SDK_URL="https://dl.google.com/android/repository/commandlinetools-win-9477386_latest.zip"
            ;;
    esac

    print_info "Downloading from: $SDK_URL"
    download_file "$SDK_URL" "cmdline-tools.zip"

    # Extract and setup with proper directory structure
    unzip -q cmdline-tools.zip

    # Create proper directory structure
    if [ -d "cmdline-tools" ]; then
        # 如果latest目录已存在，先删除它
        if [ -d "cmdline-tools/latest" ]; then
            rm -rf cmdline-tools/latest
        fi

        # 创建latest目录
        mkdir -p cmdline-tools/latest

        # 移动所有内容到latest目录，但排除latest目录本身
        for item in cmdline-tools/*; do
            if [ "$(basename "$item")" != "latest" ]; then
                mv "$item" cmdline-tools/latest/
            fi
        done
    fi

    rm -f cmdline-tools.zip

    # Set sdkmanager path and update PATH
    SDKMANAGER_PATH="$ANDROID_HOME/cmdline-tools/latest/bin/$SDKMANAGER_EXECUTABLE"
    export PATH="$PATH:$ANDROID_HOME/cmdline-tools/latest/bin"

    # Verify sdkmanager exists and is executable
    if [ ! -f "$SDKMANAGER_PATH" ]; then
        print_error "sdkmanager not found at expected location: $SDKMANAGER_PATH"
        print_info "Available files in bin directory:"
        ls -la "$ANDROID_HOME/cmdline-tools/latest/bin/" || true
        exit 1
    fi

    # 在非Windows系统上设置执行权限
    if [ "$OS" != "windows" ]; then
        chmod +x "$SDKMANAGER_PATH"
    fi
}

# Function to setup Android SDK path
setup_android_paths() {
    case $OS in
        "mac")
            ANDROID_HOME="$HOME/Library/Android/sdk"
            ;;
        "linux")
            ANDROID_HOME="$HOME/Android/Sdk"
            ;;
        "windows")
            # 处理Windows路径，支持多种格式
            if [[ "$HOME" == /c/* ]]; then
                # Git Bash 格式 /c/Users/<USER>
                ANDROID_HOME="$HOME/AppData/Local/Android/Sdk"
            elif [[ "$HOME" == /mnt/c/* ]]; then
                # WSL 格式 /mnt/c/Users/<USER>
                ANDROID_HOME="$HOME/AppData/Local/Android/Sdk"
            else
                # 标准格式
                ANDROID_HOME="$HOME/AppData/Local/Android/Sdk"
            fi
            ;;
    esac

    export ANDROID_HOME
    export PATH="$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools:$ANDROID_HOME/build-tools/$BUILD_TOOLS_VERSION"

    print_info "Android SDK path set to: $ANDROID_HOME"
}

# Function to check and install Android SDK
check_install_android_sdk() {
    print_info "Checking Android SDK installation..."

    setup_android_paths

    # Check for existing SDK with multiple possible locations
    SDKMANAGER_PATH=""
    SDKMANAGER_EXECUTABLE="sdkmanager"

    # Windows需要.bat扩展名
    if [ "$OS" = "windows" ]; then
        SDKMANAGER_EXECUTABLE="sdkmanager.bat"
    fi

    if [ -f "$ANDROID_HOME/cmdline-tools/latest/bin/$SDKMANAGER_EXECUTABLE" ]; then
        SDKMANAGER_PATH="$ANDROID_HOME/cmdline-tools/latest/bin/$SDKMANAGER_EXECUTABLE"
    elif [ -f "$ANDROID_HOME/tools/bin/$SDKMANAGER_EXECUTABLE" ]; then
        SDKMANAGER_PATH="$ANDROID_HOME/tools/bin/$SDKMANAGER_EXECUTABLE"
    fi

    # 检查SDK是否已存在且完整
    if [ -d "$ANDROID_HOME" ] && [ -n "$SDKMANAGER_PATH" ] && [ -f "$SDKMANAGER_PATH" ]; then
        # 验证SDK是否完整，检查必要的组件
        if [ -d "$ANDROID_HOME/platform-tools" ] && [ -d "$ANDROID_HOME/platforms" ]; then
            print_success "Android SDK found at $ANDROID_HOME"
            export PATH="$PATH:$(dirname $SDKMANAGER_PATH)"
            return 0
        else
            print_warning "Android SDK found but incomplete, will reinstall required components"
        fi
    fi
    
    print_info "Installing Android SDK..."

    # Create Android SDK directory
    mkdir -p "$ANDROID_HOME"
    cd "$ANDROID_HOME"

    # 如果cmdline-tools目录已存在但结构不正确，先清理
    if [ -d "cmdline-tools" ] && [ ! -f "cmdline-tools/latest/bin/$SDKMANAGER_EXECUTABLE" ]; then
        print_warning "Found incomplete cmdline-tools installation, cleaning up..."
        rm -rf cmdline-tools
    fi

    # 如果已经有正确的cmdline-tools，跳过下载
    if [ -f "cmdline-tools/latest/bin/$SDKMANAGER_EXECUTABLE" ]; then
        print_info "Command line tools already exist, skipping download"
        SDKMANAGER_PATH="$ANDROID_HOME/cmdline-tools/latest/bin/$SDKMANAGER_EXECUTABLE"
        export PATH="$PATH:$ANDROID_HOME/cmdline-tools/latest/bin"
    else
        # 下载并安装cmdline-tools
        download_and_install_cmdline_tools
    fi
    
    # Accept licenses and install required packages
    print_info "Installing Android SDK packages..."
    print_info "Accepting Android SDK licenses..."
    yes | "$SDKMANAGER_PATH" --licenses >/dev/null 2>&1 || true
    
    print_info "Installing platform-tools, platforms, and build-tools..."
    "$SDKMANAGER_PATH" "platform-tools" "platforms;android-$ANDROID_API_LEVEL" "build-tools;$BUILD_TOOLS_VERSION"
    
    print_success "Android SDK installation completed"
}

# Function to verify Android setup
verify_android_setup() {
    print_info "Verifying Android setup..."

    # 验证ANDROID_HOME环境变量
    if [ -z "$ANDROID_HOME" ]; then
        print_error "ANDROID_HOME is not set"
        return 1
    fi

    if [ ! -d "$ANDROID_HOME" ]; then
        print_error "ANDROID_HOME directory does not exist: $ANDROID_HOME"
        return 1
    fi

    # 验证sdkmanager
    SDKMANAGER_EXECUTABLE="sdkmanager"
    if [ "$OS" = "windows" ]; then
        SDKMANAGER_EXECUTABLE="sdkmanager.bat"
    fi

    SDKMANAGER_PATH="$ANDROID_HOME/cmdline-tools/latest/bin/$SDKMANAGER_EXECUTABLE"
    if [ ! -f "$SDKMANAGER_PATH" ]; then
        print_error "sdkmanager not found at: $SDKMANAGER_PATH"
        return 1
    fi

    # 验证platform-tools
    if [ ! -d "$ANDROID_HOME/platform-tools" ]; then
        print_warning "platform-tools not found, installing..."
        "$SDKMANAGER_PATH" "platform-tools"
    fi

    # 验证adb
    ADB_PATH="$ANDROID_HOME/platform-tools/adb"
    if [ "$OS" = "windows" ]; then
        ADB_PATH="$ANDROID_HOME/platform-tools/adb.exe"
    fi

    if [ ! -f "$ADB_PATH" ]; then
        print_error "adb not found at: $ADB_PATH"
        return 1
    fi

    # 验证build-tools
    if [ ! -d "$ANDROID_HOME/build-tools/$BUILD_TOOLS_VERSION" ]; then
        print_warning "build-tools $BUILD_TOOLS_VERSION not found, installing..."
        "$SDKMANAGER_PATH" "build-tools;$BUILD_TOOLS_VERSION"
    fi

    # 验证platform
    if [ ! -d "$ANDROID_HOME/platforms/android-$ANDROID_API_LEVEL" ]; then
        print_warning "platform android-$ANDROID_API_LEVEL not found, installing..."
        "$SDKMANAGER_PATH" "platforms;android-$ANDROID_API_LEVEL"
    fi

    print_success "Android setup verification completed"
    print_info "ANDROID_HOME: $ANDROID_HOME"
    print_info "SDK Manager: $SDKMANAGER_PATH"
    print_info "ADB: $ADB_PATH"
}

# Function to build APK
build_apk() {
    local project_path=$1
    local keystore_path=$2
    
    print_info "Starting APK build process..."
    
    if [ ! -d "$project_path" ]; then
        print_error "Project path does not exist: $project_path"
        exit 1
    fi
    
    cd "$project_path"
    
    # Check if it's a Gradle project
    if [ -f "gradlew" ]; then
        print_info "Building with Gradle..."
        chmod +x gradlew
        
        if [ -n "$keystore_path" ] && [ -f "$keystore_path" ]; then
            print_info "Building signed release APK..."
            read -p "Enter keystore password: " -s KEYSTORE_PASSWORD
            echo
            read -p "Enter key alias: " KEY_ALIAS
            read -p "Enter key password: " -s KEY_PASSWORD
            echo
            
            ./gradlew assembleRelease \
                -Pandroid.injected.signing.store.file="$keystore_path" \
                -Pandroid.injected.signing.store.password="$KEYSTORE_PASSWORD" \
                -Pandroid.injected.signing.key.alias="$KEY_ALIAS" \
                -Pandroid.injected.signing.key.password="$KEY_PASSWORD"
        else
            print_info "Building debug APK..."
            ./gradlew assembleDebug
        fi
        
        # Find and display APK location
        APK_PATH=$(find . -name "*.apk" | head -1)
        if [ -n "$APK_PATH" ]; then
            print_success "APK built successfully: $APK_PATH"
        else
            print_error "APK not found after build"
            exit 1
        fi
        
    else
        print_error "Gradle wrapper not found. This script supports Gradle-based Android projects only."
        exit 1
    fi
}

# Function to build AAB
build_aab() {
    local project_path=$1
    local keystore_path=$2
    
    print_info "Starting AAB build process..."
    
    if [ ! -d "$project_path" ]; then
        print_error "Project path does not exist: $project_path"
        exit 1
    fi
    
    cd "$project_path"
    
    # Check if it's a Gradle project
    if [ -f "gradlew" ]; then
        print_info "Building with Gradle..."
        chmod +x gradlew
        
        if [ -n "$keystore_path" ] && [ -f "$keystore_path" ]; then
            print_info "Building signed release AAB..."
            read -p "Enter keystore password: " -s KEYSTORE_PASSWORD
            echo
            read -p "Enter key alias: " KEY_ALIAS
            read -p "Enter key password: " -s KEY_PASSWORD
            echo
            
            ./gradlew bundleRelease \
                -Pandroid.injected.signing.store.file="$keystore_path" \
                -Pandroid.injected.signing.store.password="$KEYSTORE_PASSWORD" \
                -Pandroid.injected.signing.key.alias="$KEY_ALIAS" \
                -Pandroid.injected.signing.key.password="$KEY_PASSWORD"
        else
            print_info "Building debug AAB..."
            ./gradlew bundleDebug
        fi
        
        # Find and display AAB location
        AAB_PATH=$(find . -name "*.aab" | head -1)
        if [ -n "$AAB_PATH" ]; then
            print_success "AAB built successfully: $AAB_PATH"
        else
            print_error "AAB not found after build"
            exit 1
        fi
        
    else
        print_error "Gradle wrapper not found. This script supports Gradle-based Android projects only."
        exit 1
    fi
}

# Function to display usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --project-path PATH    Path to Android project (default: current directory)"
    echo "  -k, --keystore-path PATH   Path to keystore file for signing (optional)"
    echo "  -b, --build-type TYPE      Build type (apk or aab, default: apk)"
    echo "  -h, --help                 Show this help message"
    echo ""
    echo "This script will:"
    echo "  1. Detect your operating system"
    echo "  2. Check and install Java if needed"
    echo "  3. Check and install Android SDK if needed"
    echo "  4. Build the Android project"
}

# Main function
main() {
    local project_path="$DEFAULT_PROJECT_PATH"
    local keystore_path="$DEFAULT_KEYSTORE_PATH"
    local build_type="$DEFAULT_BUILD_TYPE"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--project-path)
                project_path="$2"
                shift 2
                ;;
            -k|--keystore-path)
                keystore_path="$2"
                shift 2
                ;;
            -b|--build-type)
                build_type="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_info "Android Cross-Platform Build Script"
    print_info "===================================="
    
    # Step 1: Detect OS
    detect_os
    
    # Step 2: Check and install Java
    check_install_java
    
    # Step 3: Check and install Android SDK
    check_install_android_sdk
    
    # Step 3.5: Verify Android setup
    verify_android_setup
    
    # Step 4: Build APK or AAB based on build type
    if [ "$build_type" = "apk" ]; then
        build_apk "$project_path" "$keystore_path"
    elif [ "$build_type" = "aab" ]; then
        build_aab "$project_path" "$keystore_path"
    else
        print_error "Invalid build type: $build_type. Supported types: apk, aab"
        exit 1
    fi
    
    print_success "Build process completed!"
}

# Run main function with all arguments
main "$@"
