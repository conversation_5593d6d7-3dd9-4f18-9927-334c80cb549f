#!/bin/bash

# 测试Android SDK安装脚本
# 用于验证修复后的android_cross_build.sh是否正常工作

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[TEST INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[TEST SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[TEST WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[TEST ERROR]${NC} $1"
}

# Function to detect operating system
detect_os() {
    case "$OSTYPE" in
        darwin*)  OS="mac" ;;
        linux*)   OS="linux" ;;
        msys*)    OS="windows" ;;
        cygwin*)  OS="windows" ;;
        *)        
            print_error "Unsupported operating system: '$OSTYPE'"
            exit 1
            ;;
    esac
    print_info "Detected OS: $OS"
}

# Function to setup Android SDK path
setup_android_paths() {
    case $OS in
        "mac")
            ANDROID_HOME="$HOME/Library/Android/sdk"
            ;;
        "linux")
            ANDROID_HOME="$HOME/Android/Sdk"
            ;;
        "windows")
            # 处理Windows路径，支持多种格式
            if [[ "$HOME" == /c/* ]]; then
                # Git Bash 格式 /c/Users/<USER>
                ANDROID_HOME="$HOME/AppData/Local/Android/Sdk"
            elif [[ "$HOME" == /mnt/c/* ]]; then
                # WSL 格式 /mnt/c/Users/<USER>
                ANDROID_HOME="$HOME/AppData/Local/Android/Sdk"
            else
                # 标准格式
                ANDROID_HOME="$HOME/AppData/Local/Android/Sdk"
            fi
            ;;
    esac
    
    export ANDROID_HOME
    print_info "Android SDK path set to: $ANDROID_HOME"
}

# Function to test SDK detection
test_sdk_detection() {
    print_info "Testing Android SDK detection..."
    
    setup_android_paths
    
    # Check for existing SDK with multiple possible locations
    SDKMANAGER_PATH=""
    SDKMANAGER_EXECUTABLE="sdkmanager"
    
    # Windows需要.bat扩展名
    if [ "$OS" = "windows" ]; then
        SDKMANAGER_EXECUTABLE="sdkmanager.bat"
    fi
    
    print_info "Looking for sdkmanager executable: $SDKMANAGER_EXECUTABLE"
    
    if [ -f "$ANDROID_HOME/cmdline-tools/latest/bin/$SDKMANAGER_EXECUTABLE" ]; then
        SDKMANAGER_PATH="$ANDROID_HOME/cmdline-tools/latest/bin/$SDKMANAGER_EXECUTABLE"
        print_success "Found sdkmanager at: $SDKMANAGER_PATH"
    elif [ -f "$ANDROID_HOME/tools/bin/$SDKMANAGER_EXECUTABLE" ]; then
        SDKMANAGER_PATH="$ANDROID_HOME/tools/bin/$SDKMANAGER_EXECUTABLE"
        print_success "Found sdkmanager at: $SDKMANAGER_PATH"
    else
        print_warning "sdkmanager not found"
        print_info "Checking if ANDROID_HOME directory exists: $ANDROID_HOME"
        if [ -d "$ANDROID_HOME" ]; then
            print_info "ANDROID_HOME exists, listing contents:"
            ls -la "$ANDROID_HOME" || true
            if [ -d "$ANDROID_HOME/cmdline-tools" ]; then
                print_info "cmdline-tools directory exists, listing contents:"
                ls -la "$ANDROID_HOME/cmdline-tools" || true
                if [ -d "$ANDROID_HOME/cmdline-tools/latest" ]; then
                    print_info "latest directory exists, listing contents:"
                    ls -la "$ANDROID_HOME/cmdline-tools/latest" || true
                    if [ -d "$ANDROID_HOME/cmdline-tools/latest/bin" ]; then
                        print_info "bin directory exists, listing contents:"
                        ls -la "$ANDROID_HOME/cmdline-tools/latest/bin" || true
                    fi
                fi
            fi
        else
            print_info "ANDROID_HOME does not exist"
        fi
    fi
    
    # 检查其他重要组件
    print_info "Checking for other Android SDK components..."
    
    if [ -d "$ANDROID_HOME/platform-tools" ]; then
        print_success "platform-tools found"
        ADB_PATH="$ANDROID_HOME/platform-tools/adb"
        if [ "$OS" = "windows" ]; then
            ADB_PATH="$ANDROID_HOME/platform-tools/adb.exe"
        fi
        if [ -f "$ADB_PATH" ]; then
            print_success "adb found at: $ADB_PATH"
        else
            print_warning "adb not found at: $ADB_PATH"
        fi
    else
        print_warning "platform-tools not found"
    fi
    
    if [ -d "$ANDROID_HOME/platforms" ]; then
        print_success "platforms directory found"
        print_info "Available platforms:"
        ls "$ANDROID_HOME/platforms" || true
    else
        print_warning "platforms directory not found"
    fi
    
    if [ -d "$ANDROID_HOME/build-tools" ]; then
        print_success "build-tools directory found"
        print_info "Available build-tools versions:"
        ls "$ANDROID_HOME/build-tools" || true
    else
        print_warning "build-tools directory not found"
    fi
}

# Main function
main() {
    print_info "Android SDK Detection Test"
    print_info "=========================="
    
    # Step 1: Detect OS
    detect_os
    
    # Step 2: Test SDK detection
    test_sdk_detection
    
    print_success "Test completed!"
}

# Run main function
main "$@"
