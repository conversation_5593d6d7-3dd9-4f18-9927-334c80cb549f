package com.flutterup.billinghelper.handler

import com.android.billingclient.api.Purchase
import com.flutterup.billinghelper.model.PurchaseEntity

/**
 * 购买处理器接口
 * 由外部模块实现，用于处理后端验证逻辑
 */
interface PurchaseHandler {

    /**
     * 验证购买
     * @param purchase 购买记录
     * @return 验证结果，true表示验证成功，false表示验证失败
     */
    suspend fun verifyPurchase(purchase: PurchaseEntity): Boolean

    /**
     * 处理验证失败的情况
     * @param purchase 购买记录
     * @param error 错误信息
     */
    suspend fun onVerificationFailed(purchase: PurchaseEntity, error: Throwable?)

    /**
     * 处理验证成功的情况
     * @param purchase 购买记录
     */
    suspend fun onVerificationSuccess(purchase: PurchaseEntity)
}
