package com.flutterup.base.utils

/**
 * 将数字转换为限制小数位数的字符串
 * @param limit 小数位数限制，默认为2
 * @param isReduce 是否需要减少小数位数的0，默认为true
 * @return 限制小数位数的字符串
 */
fun Number?.toDecimalSubstring(
    limit: Int = 2,
    isReduce: Boolean = true
): String {
    if (this == null) return ""

    // 如果 limit 小于等于 0，则直接取整数部分
    if (limit <= 0) {
        return this.toLong().toString() // 返回去掉小数部分的字符串
    }
    // 格式化字符串，保留指定的小数位数
    val formatStr = "% .${limit}f".replace(" ", "") // 修正避免空格影响
    val rawResult = formatStr.format(this.toFloat())
    // 是否需要去除末尾的小数0
    return if (isReduce) {
        // 去除小数点后多余的0和可能的多余小数点
        rawResult.trimEnd('0').trimEnd('.')
    } else {
        rawResult
    }
}