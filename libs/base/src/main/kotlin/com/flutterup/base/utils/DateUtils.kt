package com.flutterup.base.utils

import android.icu.util.TimeZone
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.Period
import java.util.Calendar
import java.util.concurrent.TimeUnit

object DateUtils {

    private const val MILLISECONDS_TIMESTAMP = 10000000000L
    private const val MILLISECONDS = 1000
    private const val MINUTES = 60
    private const val HOURS = 60
    private const val DAYS = 24
    private const val YEARS = 365
    private const val ONE_DAY = DAYS * HOURS * MINUTES * MILLISECONDS

    /**
     * 服务端常用的时间format
     */
    val defaultServerDateFormat = SimpleDateFormat("yyyy-MM-dd", LocaleUtils.currentLocale)
    val defaultServerDateTimeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", LocaleUtils.currentLocale)

    val defaultFileDateFormat = SimpleDateFormat("yyyyMMddHHmmss", LocaleUtils.currentLocale)
    val defaultHourMinuteFormat = SimpleDateFormat("hh:mm a", LocaleUtils.currentLocale)

    val defaultCountdownFormat = SimpleDateFormat("HH:mm:ss", LocaleUtils.currentLocale)

    fun formatTimestamp(timestamp: Long, checked: Boolean, format: SimpleDateFormat = defaultServerDateTimeFormat): String {
        // Automatically detect if timestamp is in seconds (10 digits) or milliseconds (13 digits)
        val timestamp = if (timestamp < MILLISECONDS_TIMESTAMP && checked) timestamp * 1000 else timestamp

        return try {
            val calendar = Calendar.getInstance(LocaleUtils.currentLocale).apply {
                timeInMillis = timestamp
            }
            format.format(calendar.time)
        } catch (_: Exception) {
            ""
        }
    }

    fun formatTimestamp(timestamp: Long, format: SimpleDateFormat = defaultServerDateTimeFormat): String {
        return formatTimestamp(timestamp, checked = true, format)
    }


    fun timestamp2Ago(timestamp: Long?): String {
        if (timestamp == null || timestamp <= 0) return ""

        val currentTime = System.currentTimeMillis()
        val diffTime = currentTime - timestamp

        return if (diffTime < ONE_DAY) {
            // 不足一天显示具体时间 (14:12)
            formatTimestamp(timestamp, defaultHourMinuteFormat)
        } else {
            // 计算天数
            val days = (diffTime / ONE_DAY).toInt()
            // 显示"x days ago"
            if (days == 1) "1 day ago" else "$days days ago"
        }
    }

    fun formatBirthday(birthday: Long, format: SimpleDateFormat = defaultServerDateFormat): String {
        return format.format(birthday)
    }

    fun getAgeFromLocalDate(date: LocalDate): Int {
        val today = LocalDate.now()
        return Period.between(date, today).years
    }

    /**
     * 将时间戳转换为详细的时间字符串
     * 今天: 2:12 pm
     * 昨天: Yesterday 2:12 pm
     * 其他: 2021-01-01 2:12 pm
     * 
     * @param timestamp 时间戳
     * @return 详细的时间字符串
     */
    fun timestampToDetail(timestamp: Long): String {
        if (timestamp <= 0) return ""
        
        // 自动检测时间戳格式
        val actualTimestamp = if (timestamp < MILLISECONDS_TIMESTAMP) timestamp * 1000 else timestamp
        
        val calendar = Calendar.getInstance(LocaleUtils.currentLocale).apply {
            timeInMillis = actualTimestamp
        }
        val now = Calendar.getInstance(LocaleUtils.currentLocale)
        val timeStr = defaultHourMinuteFormat.format(calendar.time)
        
        return when {
            // 今天
            isSameDay(actualTimestamp, now.timeInMillis) -> timeStr
            
            // 昨天
            isYesterday(actualTimestamp, now.timeInMillis) -> "Yesterday $timeStr"
            
            // 其他日期
            else -> {
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", LocaleUtils.currentLocale)
                "${dateFormat.format(calendar.time)} $timeStr"
            }
        }
    }

    /**
     * 判断两个时间戳是否在指定的时间间隔内
     * @param pre 之前的时间戳
     * @param current 当前的时间戳
     * @param interval 时间间隔
     * @param intervalUnit 时间间隔的单位
     * @return true: 在时间间隔内，false: 不在时间间隔内
     */
    fun belowTimeInterval(
        pre: Long,
        current: Long,
        interval: Long,
        intervalUnit: TimeUnit = TimeUnit.MILLISECONDS
    ): Boolean {
        return current - pre < intervalUnit.toMillis(interval)
    }

    /**
     * 是否是同一天
     */
    fun isSameDay(pre: Long, current: Long): Boolean {
        val calendar1 = Calendar.getInstance(LocaleUtils.currentLocale).apply {
            timeInMillis = if (pre < MILLISECONDS_TIMESTAMP) pre * 1000 else pre
        }
        val calendar2 = Calendar.getInstance(LocaleUtils.currentLocale).apply {
            timeInMillis = if (current < MILLISECONDS_TIMESTAMP) current * 1000 else current
        }
        
        return calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR) &&
               calendar1.get(Calendar.DAY_OF_YEAR) == calendar2.get(Calendar.DAY_OF_YEAR)
    }

    private fun isYesterday(timestamp: Long, currentTimestamp: Long): Boolean {
        val yesterday = Calendar.getInstance(LocaleUtils.currentLocale).apply {
            timeInMillis = currentTimestamp
            add(Calendar.DAY_OF_YEAR, -1)
        }
        return isSameDay(timestamp, yesterday.timeInMillis)
    }
}