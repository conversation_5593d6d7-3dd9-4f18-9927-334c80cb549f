package com.flutterup.gifts.entity

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass


@Keep
@JsonClass(generateAdapter = true)
data class GiftResourceInfo(

    @<PERSON><PERSON>(name = "id")
    val giftId: String,

    @<PERSON><PERSON>(name = "name")
    val name: String? = null,

    @<PERSON><PERSON>(name = "price")
    val price: Int? = null,

    @<PERSON><PERSON>(name = "img_url")
    val image: String? = null,

    @<PERSON><PERSON>(name = "gif_url")
    val gif: String? = null,

    @<PERSON><PERSON>(name = "video_url")
    val video: String? = null,

    @<PERSON><PERSON>(name = "desc")
    val desc: String? = null,

    @<PERSON><PERSON>(name = "version")
    val version: Long = 0L,

    @<PERSON><PERSON>(name = "exchange_id")
    val exchangeId: String? = null,

    @<PERSON><PERSON>(name = "original_msgid")
    val originalMsgId: String? = null
)
